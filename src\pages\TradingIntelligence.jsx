import React, { useState, useRef, useEffect } from 'react';
import {
  FaPlay,
  FaPause,
  FaVolumeUp,
  FaVolumeOff,
  FaExpand,
  FaChartLine,
  FaBrain,
  FaRobot,
  FaShieldAlt,
  FaRocket,
  FaCheckCircle,
  FaEnvelope,
  FaPhone,
  FaArrowRight,
  FaFastForward,
  FaFastBackward,
  FaTrendingUp,
  FaDatabase,
  FaCog,
  FaLightbulb
} from 'react-icons/fa';
import { gsap } from 'gsap';
import 'bootstrap/dist/css/bootstrap.min.css';

const TradingIntelligence = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(1);
  const videoRef = useRef(null);
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);
  const visualizationRef = useRef(null);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (e) => {
    if (videoRef.current && duration) {
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const clickRatio = clickX / rect.width;
      const newTime = clickRatio * duration;
      videoRef.current.currentTime = newTime;
    }
  };

  const handleVolumeToggle = () => {
    if (videoRef.current) {
      if (isMuted) {
        videoRef.current.muted = false;
        videoRef.current.volume = volume;
        setIsMuted(false);
      } else {
        setVolume(videoRef.current.volume);
        videoRef.current.muted = true;
        setIsMuted(true);
      }
    }
  };

  const handleFullscreen = () => {
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      } else if (videoRef.current.webkitRequestFullscreen) {
        videoRef.current.webkitRequestFullscreen();
      } else if (videoRef.current.msRequestFullscreen) {
        videoRef.current.msRequestFullscreen();
      }
    }
  };

  const handleSkipForward = () => {
    if (videoRef.current) {
      videoRef.current.currentTime = Math.min(videoRef.current.currentTime + 15, duration);
    }
  };

  const handleSkipBackward = () => {
    if (videoRef.current) {
      videoRef.current.currentTime = Math.max(videoRef.current.currentTime - 15, 0);
    }
  };

  // Hero section animations with slide-in effects
  useEffect(() => {
    const ctx = gsap.context(() => {
      const tl = gsap.timeline({ delay: 0.2 });

      // Title slides in from left with rotation
      if (titleRef.current) {
        tl.from(titleRef.current, {
          x: -200,
          opacity: 0,
          rotation: -10,
          duration: 1.5,
          ease: "power3.out"
        });
      }

      // Subtitle slides in from right
      if (subtitleRef.current) {
        tl.from(subtitleRef.current, {
          x: 200,
          opacity: 0,
          duration: 1.2,
          ease: "power2.out"
        }, "-=1");
      }

      // CTA bounces in from bottom
      if (ctaRef.current) {
        tl.from(ctaRef.current, {
          y: 100,
          opacity: 0,
          scale: 0.8,
          duration: 1,
          ease: "bounce.out"
        }, "-=0.8");
      }

      // Visualization rotates in
      if (visualizationRef.current) {
        tl.from(visualizationRef.current, {
          scale: 0,
          rotation: 180,
          opacity: 0,
          duration: 1.5,
          ease: "back.out(1.7)"
        }, "-=1.2");
      }
    }, heroRef);

    return () => ctx.revert();
  }, []);

  const features = [
    {
      icon: FaBrain,
      title: "AI-Powered Analytics",
      description: "Advanced machine learning algorithms analyze market patterns and predict trading opportunities with unprecedented accuracy."
    },
    {
      icon: FaTrendingUp,
      title: "Real-Time Market Intelligence",
      description: "Get instant insights into market movements, trends, and opportunities with our real-time data processing engine."
    },
    {
      icon: FaRobot,
      title: "Automated Trading Strategies",
      description: "Deploy sophisticated automated trading strategies that adapt to market conditions and optimize performance continuously."
    },
    {
      icon: FaShieldAlt,
      title: "Risk Management",
      description: "Comprehensive risk assessment and management tools to protect your investments and maximize returns."
    }
  ];

  const benefits = [
    "Advanced algorithmic trading capabilities",
    "Real-time market data and analytics",
    "Customizable trading dashboards",
    "Risk management and compliance tools",
    "Multi-asset class support",
    "API integrations with major exchanges"
  ];

  return (
    <div className="trading-intelligence-page">
      <style jsx>{`
        .trading-intelligence-page {
          overflow-x: hidden;
        }

        .hero-section {
          position: relative;
        }

        .hero-section::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
          opacity: 0.3;
          pointer-events: none;
        }

        /* --- Animations --- */
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes pulse {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.8;
                box-shadow: 0 0 40px rgba(0, 160, 233, 0.4);
            }
            50% {
                transform: translate(-50%, -50%) scale(1.05);
                opacity: 1;
                box-shadow: 0 0 70px rgba(0, 160, 233, 0.6);
            }
            100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.8;
                box-shadow: 0 0 40px rgba(0, 160, 233, 0.4);
            }
        }

        @keyframes orbit {
          from { transform: translate(-50%, -50%) rotate(0deg) translateX(var(--radius)) rotate(0deg); }
          to { transform: translate(-50%, -50%) rotate(360deg) translateX(var(--radius)) rotate(-360deg); }
        }

        @keyframes dash {
          to { stroke-dashoffset: -20; }
        }

        @keyframes particleFloat {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg);
                opacity: 0.6;
            }
            33% {
                transform: translateY(-20px) translateX(10px) rotate(120deg);
                opacity: 1;
            }
            66% {
                transform: translateY(10px) translateX(-15px) rotate(240deg);
                opacity: 0.8;
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg);
                opacity: 0.7;
            }
            33% {
                transform: translateY(-25px) translateX(15px) rotate(100deg);
                opacity: 1;
            }
            66% {
                transform: translateY(10px) translateX(-20px) rotate(200deg);
                opacity: 0.8;
            }
        }

        /* --- Hero Section Styles --- */
        .hero-section {
          background-attachment: fixed;
          background-size: cover;
          background-position: center;
        }

        .display-1 {
            letter-spacing: -1px;
        }

        .lead {
            font-weight: 300;
        }

        /* Hero stats animation delay */
        .hero-section > .container > .row > .col-lg-6 {
          animation: fadeInUp 0.8s ease-out;
        }

        .hero-section > .container > .row > .col-lg-6:nth-child(2) {
          animation-delay: 0.2s;
        }

        /* --- Video Section Styles --- */
        .video-section .col-lg-10 {
          animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .video-container {
            background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
            aspect-ratio: 16/9;
            box-shadow: 0 20px 60px rgba(0, 43, 89, 0.3), 0 8px 25px rgba(0, 157, 230, 0.2);
            border: 2px solid rgba(0, 157, 230, 0.1);
            position: relative;
            overflow: hidden;
        }

        .video-container::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent 30%, rgba(0, 157, 230, 0.05) 50%, transparent 70%);
          pointer-events: none;
          z-index: 1;
        }

        /* Video controls */
        .video-controls {
          opacity: 0;
          transform: translateY(20px);
          transition: all 0.4s ease-out;
          pointer-events: none;
          backdrop-filter: blur(15px);
          background: linear-gradient(transparent, rgba(0,0,0,0.9)) !important;
        }

        .video-container:hover .video-controls {
          opacity: 1;
          transform: translateY(0);
          pointer-events: all;
        }

        /* Play/Pause, Skip buttons */
        .video-controls .btn {
            font-size: 1.2rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 45px;
            height: 45px;
            transition: all 0.3s ease;
        }

        .video-controls .btn:hover {
            background: rgba(0, 157, 230, 0.8) !important;
            transform: scale(1.1);
        }

        /* Progress bar */
        .progress {
            height: 8px !important;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            transition: all 0.3s ease-in-out !important;
        }

        .progress:hover {
            transform: scaleY(1.5);
        }

        .progress-bar {
            background: linear-gradient(90deg, #002B59 0%, #009DE6 100%) !important;
            border-radius: 3px;
            transition: width 0.1s ease;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            transform: translate(-50%, -50%);
            width: 12px;
            height: 12px;
            background: #fff;
            box-shadow: 0 0 10px rgba(0, 157, 230, 0.7);
            border-radius: 50%;
            opacity: 1;
            pointer-events: none;
            z-index: 1;
        }

        /* Play button overlay */
        .video-container .btn.rounded-circle.position-absolute {
            transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
            box-shadow: 0 15px 35px rgba(0, 43, 89, 0.4), 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .video-container .btn.rounded-circle.position-absolute:hover {
            transform: scale(1.15);
            box-shadow: 0 20px 45px rgba(0, 157, 230, 0.6), 0 10px 25px rgba(0, 0, 0, 0.4);
        }

        /* --- Features Section Styles --- */
        .features-section {
          padding-top: 5rem;
          padding-bottom: 5rem;
          background-color: #f8f9fa;
        }

        .feature-card {
            backdrop-filter: blur(10px);
            background: white;
            border: 1px solid rgba(0, 157, 230, 0.1) !important;
            box-shadow: 0 4px 15px rgba(0, 43, 89, 0.06);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.6s ease-out both;
            padding: 1.5rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .feature-list-container > div:last-child .feature-card {
            margin-bottom: 0;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0, 43, 89, 0.15), 0 5px 15px rgba(0, 157, 230, 0.1);
            border-color: rgba(0, 157, 230, 0.5) !important;
        }

        .feature-card:nth-child(1) { animation-delay: 0.1s; }
        .feature-card:nth-child(2) { animation-delay: 0.2s; }
        .feature-card:nth-child(3) { animation-delay: 0.3s; }
        .feature-card:nth-child(4) { animation-delay: 0.4s; }

        .feature-icon {
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 157, 230, 0.25);
            width: 55px;
            height: 55px;
            min-width: 55px;
            min-height: 55px;
            border-radius: 0.5rem;
            margin-right: 1.25rem;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1);
            box-shadow: 0 6px 18px rgba(0, 157, 230, 0.4);
        }

        .feature-card h5 {
            color: #002B59;
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
        }

        .feature-card p {
            color: #6c757d;
            font-size: 1rem;
            line-height: 1.6;
        }

        /* Benefits Card */
        .benefits-card {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #002B59 0%, #009DE6 100%);
            color: white;
            box-shadow: 0 15px 40px rgba(0, 43, 89, 0.3);
            border: none;
            padding: 2.5rem;
            border-radius: 0.75rem;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .benefits-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
          opacity: 0.5;
          pointer-events: none;
        }

        .benefits-card h3 {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
        }

        .benefits-list .fa-check-circle {
            transition: transform 0.2s ease-out;
            color: #00ff88;
            font-size: 1.1rem;
            min-width: 20px;
        }

        .benefits-list div:hover .fa-check-circle {
            transform: scale(1.2);
            color: #39ff14 !important;
        }

        .benefits-list span {
            font-size: 1rem;
            line-height: 1.6;
            flex-grow: 1;
        }

        /* General Button Hover Effect */
        .btn:hover {
          transform: translateY(-2px);
          transition: transform 0.2s ease;
        }

        /* Responsive Adjustments */
        @media (max-width: 991.98px) {
            .features-section .row {
                flex-direction: column;
            }

            .col-lg-8, .col-lg-4 {
                width: 100%;
                max-width: 100%;
                margin-bottom: 2rem;
            }

            .benefits-card {
                margin-bottom: 0;
            }

            .feature-card {
                margin-bottom: 1.5rem;
            }
        }

        @media (max-width: 767.98px) {
            .features-section {
                padding-top: 3rem;
                padding-bottom: 3rem;
            }

            .feature-card {
                flex-direction: column;
                text-align: center;
                padding: 1.25rem;
            }

            .feature-icon {
                margin-right: 0;
                margin-bottom: 1rem;
            }

            .feature-card h5 {
                font-size: 1.15rem;
            }

            .feature-card p {
                font-size: 0.9rem;
            }

            .benefits-card {
                padding: 2rem;
            }

            .benefits-card h3 {
                font-size: 1.5rem;
            }

            .benefits-list div {
                align-items: flex-start;
            }
        }
      `}</style>

      {/* Extraordinary Hero Section */}
      <section
        ref={heroRef}
        className="position-relative overflow-hidden"
        style={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #002956 0%, #001a3a 50%, #000f1f 100%)',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating Geometric Shapes */}
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="position-absolute"
              style={{
                width: `${60 + Math.random() * 40}px`,
                height: `${60 + Math.random() * 40}px`,
                background: `linear-gradient(45deg, rgba(0, 160, 233, 0.1), rgba(0, 160, 233, 0.3))`,
                borderRadius: Math.random() > 0.5 ? '50%' : '20%',
                top: `${Math.random() * 80 + 10}%`,
                left: `${Math.random() * 80 + 10}%`,
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(0, 160, 233, 0.2)',
                boxShadow: '0 8px 32px rgba(0, 160, 233, 0.1)',
                animation: `float ${3 + Math.random() * 2}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 2}s`
              }}
            />
          ))}

          {/* Floating Particles */}
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="position-absolute"
              style={{
                width: '4px',
                height: '4px',
                background: '#00a0e9',
                borderRadius: '50%',
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                opacity: 0.6,
                boxShadow: '0 0 10px #00a0e9',
                animation: `particleFloat ${4 + Math.random() * 3}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 3}s`
              }}
            />
          ))}

          {/* Gradient Orbs */}
          <div
            className="position-absolute"
            style={{
              width: '300px',
              height: '300px',
              background: 'radial-gradient(circle, rgba(0, 160, 233, 0.15) 0%, transparent 70%)',
              borderRadius: '50%',
              top: '20%',
              right: '10%',
              filter: 'blur(40px)',
              animation: 'floatOrb1 6s ease-in-out infinite'
            }}
          />
          <div
            className="position-absolute"
            style={{
              width: '200px',
              height: '200px',
              background: 'radial-gradient(circle, rgba(0, 160, 233, 0.1) 0%, transparent 70%)',
              borderRadius: '50%',
              bottom: '30%',
              left: '15%',
              filter: 'blur(30px)',
              animation: 'floatOrb2 8s ease-in-out infinite'
            }}
          />
        </div>

        <div className="container position-relative" style={{ zIndex: 2 }}>
          <div className="row align-items-center min-vh-100">
            <div className="col-lg-6 text-white">
              <div className="mb-4">
                <h1
                  ref={titleRef}
                  className="display-1 fw-bold mb-4"
                  style={{
                    fontSize: 'clamp(3rem, 8vw, 4.4rem)',
                    lineHeight: '1.1',
                    background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
                  }}
                >
                  Makonis.<br />
                  <span style={{ color: '#00a0e9' }}>Trading Intelligence.</span>
                </h1>
              </div>

              <div>
                <p
                  ref={subtitleRef}
                  className="lead mb-5"
                  style={{
                    fontSize: '1.2rem',
                    lineHeight: '1.6',
                    color: 'rgba(255, 255, 255, 0.9)',
                    maxWidth: '600px'
                  }}
                >
                  Revolutionize your trading operations with our cutting-edge AI-powered
                  trading intelligence platform. Harness advanced analytics, real-time market
                  insights, and automated strategies to maximize your trading performance and
                  stay ahead of market movements.
                </p>
              </div>

              {/* Stats Section */}
              <div ref={ctaRef} className="row mt-5 pt-4">
                {[
                  { number: '95%', label: 'Accuracy Rate' },
                  { number: '24/7', label: 'Market Monitoring' },
                  { number: '500+', label: 'Trading Strategies' }
                ].map((stat, index) => (
                  <div key={index} className="col-4 text-center">
                    <div
                      className="p-3 rounded-4"
                      style={{
                        background: 'rgba(255, 255, 255, 0.05)',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        animation: `fadeInUp 0.8s ease-out ${0.6 + index * 0.2}s both`
                      }}
                    >
                      <h3
                        className="fw-bold mb-1"
                        style={{
                          color: '#00a0e9',
                          fontSize: '1.8rem'
                        }}
                      >
                        {stat.number}
                      </h3>
                      <p
                        className="small mb-0"
                        style={{
                          color: 'rgba(255, 255, 255, 0.8)',
                          fontSize: '0.95rem'
                        }}
                      >
                        {stat.label}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="col-lg-6 d-none d-lg-block">
              {/* Trading Intelligence Visualization */}
              <div
                ref={visualizationRef}
                className="position-relative"
                style={{
                  height: '600px',
                  perspective: '1000px'
                }}
              >
                {/* Central Trading Hub */}
                <div
                  className="position-absolute top-50 start-50 translate-middle"
                  style={{
                    width: '200px',
                    height: '200px',
                    background: 'linear-gradient(135deg, rgba(0, 160, 233, 0.2), rgba(0, 160, 233, 0.4))',
                    borderRadius: '50%',
                    backdropFilter: 'blur(20px)',
                    border: '2px solid rgba(0, 160, 233, 0.3)',
                    boxShadow: '0 0 60px rgba(0, 160, 233, 0.4)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    animation: 'pulse 3s ease-in-out infinite'
                  }}
                >
                  <FaChartLine
                    style={{
                      fontSize: '80px',
                      color: '#00a0e9',
                      filter: 'drop-shadow(0 0 20px rgba(0, 160, 233, 0.8))'
                    }}
                  />
                </div>

                {/* Orbiting Trading Features */}
                {[
                  { icon: FaBrain, angle: 0, radius: 150, color: '#00a0e9' },
                  { icon: FaTrendingUp, angle: 60, radius: 180, color: '#0056b3' },
                  { icon: FaRobot, angle: 120, radius: 160, color: '#00a0e9' },
                  { icon: FaDatabase, angle: 180, radius: 170, color: '#0056b3' },
                  { icon: FaCog, angle: 240, radius: 155, color: '#00a0e9' },
                  { icon: FaLightbulb, angle: 300, radius: 175, color: '#0056b3' }
                ].map((item, index) => (
                  <div
                    key={index}
                    className="position-absolute"
                    style={{
                      top: '50%',
                      left: '50%',
                      transform: `translate(-50%, -50%) rotate(${item.angle}deg) translateX(${item.radius}px) rotate(-${item.angle}deg)`,
                      width: '80px',
                      height: '80px',
                      background: `linear-gradient(135deg, ${item.color}20, ${item.color}40)`,
                      borderRadius: '20px',
                      backdropFilter: 'blur(10px)',
                      border: `1px solid ${item.color}30`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: `0 8px 32px ${item.color}20`,
                      animation: `orbit 20s linear infinite`,
                      animationDelay: `${index * -3.33}s`,
                      '--radius': `${item.radius}px`
                    }}
                  >
                    <item.icon
                      style={{
                        fontSize: '24px',
                        color: item.color
                      }}
                    />
                  </div>
                ))}

                {/* Connection Lines */}
                <svg
                  className="position-absolute top-0 start-0 w-100 h-100"
                  style={{ zIndex: -1 }}
                >
                  {[...Array(6)].map((_, i) => (
                    <line
                      key={i}
                      x1="50%"
                      y1="50%"
                      x2={`${50 + 25 * Math.cos(i * Math.PI / 3)}%`}
                      y2={`${50 + 25 * Math.sin(i * Math.PI / 3)}%`}
                      stroke="rgba(0, 160, 233, 0.3)"
                      strokeWidth="2"
                      strokeDasharray="5,5"
                      style={{
                        animation: `dash 2s linear infinite`,
                        animationDelay: `${i * 0.3}s`
                      }}
                    />
                  ))}
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Video Demo Section */}
      <section className="video-section py-5" style={{ backgroundColor: '#f8f9fa' }}>
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-10">
              <div className="text-center mb-5">
                <h2 className="display-5 fw-bold mb-4" style={{
                  color: '#002B59',
                  background: 'linear-gradient(135deg, #002B59 0%, #009DE6 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}>
                  Makonis Trading Intelligence Platform Demo
                </h2>
                <p className="lead text-muted mb-4" style={{ fontSize: '1.2rem', lineHeight: '1.6' }}>
                  Discover the power of our advanced trading intelligence platform. This comprehensive demo
                  showcases real-time market analysis, AI-powered trading strategies, risk management tools,
                  and automated execution capabilities that drive superior trading performance.
                </p>
                <div className="row justify-content-center">
                  <div className="col-lg-8">
                    <div className="d-flex flex-wrap justify-content-center gap-3 mb-4">
                      <span className="badge px-3 py-2" style={{
                        background: 'linear-gradient(90deg, #002B59 0%, #009DE6 100%)',
                        color: 'white',
                        fontSize: '14px',
                        borderRadius: '20px'
                      }}>
                        🧠 AI-Powered Analytics
                      </span>
                      <span className="badge px-3 py-2" style={{
                        background: 'linear-gradient(90deg, #002B59 0%, #009DE6 100%)',
                        color: 'white',
                        fontSize: '14px',
                        borderRadius: '20px'
                      }}>
                        📈 Real-time Market Data
                      </span>
                      <span className="badge px-3 py-2" style={{
                        background: 'linear-gradient(90deg, #002B59 0%, #009DE6 100%)',
                        color: 'white',
                        fontSize: '14px',
                        borderRadius: '20px'
                      }}>
                        🤖 Automated Trading
                      </span>
                      <span className="badge px-3 py-2" style={{
                        background: 'linear-gradient(90deg, #002B59 0%, #009DE6 100%)',
                        color: 'white',
                        fontSize: '14px',
                        borderRadius: '20px'
                      }}>
                        🛡️ Risk Management
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Custom Video Player */}
              <div
                className="video-container rounded-4 overflow-hidden"
              >
                <video
                  ref={videoRef}
                  className="w-100 h-100"
                  onTimeUpdate={handleTimeUpdate}
                  onLoadedMetadata={handleLoadedMetadata}
                  poster="https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
                  style={{ objectFit: 'contain' }}
                  preload="metadata"
                >
                  <source src="/src/Asserts/MTI.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>

                {/* Custom Video Controls */}
                <div
                  className="video-controls position-absolute bottom-0 start-0 end-0 p-4"
                >
                  <div className="d-flex align-items-center gap-4">
                    <button
                      className="btn text-white p-2 rounded-circle d-flex align-items-center justify-content-center"
                      onClick={handlePlayPause}
                    >
                      {isPlaying ? <FaPause /> : <FaPlay style={{ marginLeft: '2px' }} />}
                    </button>

                    <div className="flex-grow-1">
                      <div
                        className="progress position-relative"
                        onClick={handleProgressClick}
                      >
                        <div
                          className="progress-bar position-relative"
                          style={{
                            width: `${duration ? (currentTime / duration) * 100 : 0}%`,
                          }}
                        >
                        </div>
                      </div>
                    </div>

                    <span
                      className="text-white fw-medium"
                      style={{
                        fontSize: '14px',
                        fontFamily: 'monospace',
                        minWidth: '80px',
                        textAlign: 'center'
                      }}
                    >
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </span>

                    {/* Skip Backward Button */}
                    <button
                      className="btn text-white p-2 rounded-circle d-flex align-items-center justify-content-center"
                      onClick={handleSkipBackward}
                      title="Skip backward 15s"
                      style={{ width: '40px', height: '40px' }}
                    >
                      <FaFastBackward />
                    </button>

                    {/* Skip Forward Button */}
                    <button
                      className="btn text-white p-2 rounded-circle d-flex align-items-center justify-content-center"
                      onClick={handleSkipForward}
                      title="Skip forward 15s"
                      style={{ width: '40px', height: '40px' }}
                    >
                      <FaFastForward />
                    </button>

                    {/* Volume Control Button */}
                    <button
                      className="btn text-white p-2 rounded-circle d-flex align-items-center justify-content-center"
                      onClick={handleVolumeToggle}
                      title={isMuted ? "Unmute" : "Mute"}
                      style={{ width: '40px', height: '40px' }}
                    >
                      {isMuted ? <FaVolumeOff /> : <FaVolumeUp />}
                    </button>

                    {/* Fullscreen Button */}
                    <button
                      className="btn text-white p-2 rounded-circle d-flex align-items-center justify-content-center"
                      onClick={handleFullscreen}
                      title="Fullscreen"
                      style={{ width: '40px', height: '40px' }}
                    >
                      <FaExpand />
                    </button>
                  </div>
                </div>

                {/* Play Button Overlay */}
                {!isPlaying && (
                  <div
                    className="position-absolute top-50 start-50 translate-middle"
                    style={{ cursor: 'pointer', zIndex: 10 }}
                    onClick={handlePlayPause}
                  >
                    <div
                      className="btn rounded-circle d-flex align-items-center justify-content-center position-relative"
                    >
                      <FaPlay
                        size={28}
                        style={{
                          marginLeft: '6px',
                          color: 'white',
                          filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
                        }}
                      />

                      {/* Pulse animation ring */}
                      <div
                        className="position-absolute rounded-circle"
                        style={{
                          width: '120px',
                          height: '120px',
                          border: '2px solid rgba(0, 157, 230, 0.3)',
                          animation: 'pulse 2s infinite',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)'
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section py-5">
        <div className="container">
          <div className="row align-items-stretch">
            {/* Features List - Stacked Vertically */}
            <div className="col-lg-8 mb-5 mb-lg-0">
              <div className="h-100 d-flex flex-column justify-content-center">
                <div className="feature-list-container d-flex flex-column">
                  {features.map((feature, index) => (
                    <div key={index}>
                      <div className="feature-card">
                        <div
                          className="feature-icon d-flex align-items-center justify-content-center rounded-2 flex-shrink-0"
                          style={{
                            background: 'linear-gradient(135deg, #002B59 0%, #009DE6 100%)',
                            color: 'white',
                          }}
                        >
                          <feature.icon size={20} />
                        </div>
                        <div className="flex-grow-1">
                          <h5 className="fw-bold mb-2">
                            {feature.title}
                          </h5>
                          <p className="text-muted mb-0">
                            {feature.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Benefits Card */}
            <div className="col-lg-4">
              <div className="benefits-card">
                <h3 className="fw-bold mb-4">Why Choose Our Trading Intelligence?</h3>
                <div className="benefits-list">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="d-flex align-items-start mb-3">
                      <FaCheckCircle
                        className="me-3 mt-1 flex-shrink-0"
                        size={16}
                      />
                      <span className="flex-grow-1">
                        {benefit}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TradingIntelligence;
