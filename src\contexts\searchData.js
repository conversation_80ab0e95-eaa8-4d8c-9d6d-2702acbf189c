// Search Data Aggregator - Centralized searchable content
export const searchData = {
  // Navigation items from Header
  navigation: [
    { id: 'nav-home', title: 'Home', type: 'navigation', path: '/', keywords: ['home', 'main', 'landing'] },
    { id: 'nav-about', title: 'About Us', type: 'navigation', path: '/about-us', keywords: ['about', 'company', 'who we are'] },
    { id: 'nav-team', title: 'Our Leadership', type: 'navigation', path: '/team', keywords: ['team', 'leadership', 'founders', 'management'] },
    { id: 'nav-business', title: 'Business Model', type: 'navigation', path: '/business-model', keywords: ['business', 'model', 'approach'] },
    { id: 'nav-life', title: 'Life At Makonis', type: 'navigation', path: '/life-at-makonis', keywords: ['life', 'culture', 'work'] },
    { id: 'nav-it-services', title: 'IT Services', type: 'navigation', path: '/it-services', keywords: ['it', 'services', 'technology'] },
    { id: 'nav-staff', title: 'Staff Augmentation', type: 'navigation', path: '/staff-augmentation', keywords: ['staff', 'augmentation', 'hiring', 'talent'] },
    { id: 'nav-integration', title: 'Integration', type: 'navigation', path: '/integration', keywords: ['integration', 'systems'] },
    { id: 'nav-testing', title: 'Testing', type: 'navigation', path: '/testing', keywords: ['testing', 'qa', 'quality assurance'] },
    { id: 'nav-webdev', title: 'Web Development', type: 'navigation', path: '/webdev', keywords: ['web', 'development', 'website', 'frontend', 'backend'] },
    { id: 'nav-iot', title: 'IoT', type: 'navigation', path: '/iot', keywords: ['iot', 'internet of things', 'connected devices'] },
    { id: 'nav-ai', title: 'AI', type: 'navigation', path: '/ai', keywords: ['ai', 'artificial intelligence', 'machine learning'] },
    { id: 'nav-semiconductors', title: 'Semiconductors', type: 'navigation', path: '/semiconductors', keywords: ['semiconductors', 'chips', 'hardware'] },
    { id: 'nav-physical-design', title: 'Physical Design', type: 'navigation', path: '/physical-design', keywords: ['physical design', 'chip design', 'layout'] },
    { id: 'nav-physical-verification', title: 'Physical Verification', type: 'navigation', path: '/physical-verification', keywords: ['physical verification', 'drc', 'lvs'] },
    { id: 'nav-case-studies', title: 'Case Studies', type: 'navigation', path: '/case-studies', keywords: ['case studies', 'projects', 'examples'] },
    { id: 'nav-testimonials', title: 'Client Testimonials', type: 'navigation', path: '/testimonials', keywords: ['testimonials', 'reviews', 'clients', 'feedback'] },
    { id: 'nav-careers', title: 'Careers', type: 'navigation', path: '/careers', keywords: ['careers', 'jobs', 'hiring', 'employment'] },
    { id: 'nav-contact', title: 'Contact Us', type: 'navigation', path: '/contact', keywords: ['contact', 'reach out', 'get in touch'] }
  ],
 
  // Main services from ServicesSection
  services: [
    {
      id: 'service-ai',
      title: 'Artificial Intelligence',
      type: 'service',
      path: '/analytics',
      description: 'NLP services to extract insights from unstructured language and Computer Vision to recognize objects of interest.',
      keywords: ['ai', 'artificial intelligence', 'nlp', 'natural language processing', 'computer vision', 'machine learning', 'deep learning']
    },
    {
      id: 'service-iot',
      title: 'Internet of Things',
      type: 'service',
      path: '/iot',
      description: 'As IoT advances, the dividing line between reality and virtual reality becomes blurred in a creative way.',
      keywords: ['iot', 'internet of things', 'connected devices', 'sensors', 'smart devices', 'connectivity']
    },
    {
      id: 'service-webdev',
      title: 'Web Development',
      type: 'service',
      path: '/webdev',
      description: 'Modern web development solutions for your business needs.',
      keywords: ['web development', 'website', 'frontend', 'backend', 'full stack', 'react', 'javascript', 'html', 'css']
    },
    {
      id: 'service-testing',
      title: 'Testing',
      type: 'service',
      path: '/testing',
      description: 'Testing is an infinite process of comparing the invisible to the ambiguous in order to avoid the unthinkable happening to the anonymous.',
      keywords: ['testing', 'qa', 'quality assurance', 'automation', 'manual testing', 'functional testing', 'performance testing']
    },
    {
      id: 'service-embedded',
      title: 'Embedded Systems',
      type: 'service',
      path: '/embedded',
      description: 'Working on the integrated systems have been challenging so far, why not make it a worthwhile product eventually?',
      keywords: ['embedded systems', 'firmware', 'microcontrollers', 'hardware', 'real-time systems', 'iot devices']
    }
  ],
 
  // Team members from TeamPage
  team: [
    {
      id: 'team-krishna',
      title: 'Krishna Samanth',
      type: 'team',
      path: '/team',
      description: 'Founder & CEO - Serial Entrepreneur, over 18 years of experience managing technology business.',
      keywords: ['krishna samanth', 'founder', 'ceo', 'entrepreneur', 'leadership', 'technology business']
    },
    {
      id: 'team-durga',
      title: 'Durga Prasad',
      type: 'team',
      path: '/team',
      description: 'COO & Co-Founder - Over 30 years of experience in Global Software Delivery, Vision, Strategy and Operational Excellence.',
      keywords: ['durga prasad', 'coo', 'co-founder', 'software delivery', 'strategy', 'operations']
    },
    {
      id: 'team-sanjay',
      title: 'Sanjay Chatterjee',
      type: 'team',
      path: '/team',
      description: 'CHRO & Co-Founder - Dubai - 30 years in Global HR functions for IT (Products / Projects) and Retail sectors.',
      keywords: ['sanjay chatterjee', 'chro', 'co-founder', 'hr', 'human resources', 'dubai']
    },
    {
      id: 'team-vamshree',
      title: 'Vamshree',
      type: 'team',
      path: '/team',
      description: 'CTO & Managing Partner - Australia - 20 years in IT Development and Enterprise Delivery across Finance, Banking, Telecom, and Healthcare.',
      keywords: ['vamshree', 'cto', 'managing partner', 'australia', 'it development', 'enterprise', 'finance', 'banking', 'telecom', 'healthcare']
    },
    {
      id: 'team-sreeni',
      title: 'Sreeni Raju',
      type: 'team',
      path: '/team',
      description: 'Head – ERP Software Solutions & Delivery - USA - 22 Years of experience in core HR, global ERP implementations and consulting in some of the global fortune 500 companies.',
      keywords: ['sreeni raju', 'erp', 'software solutions', 'usa', 'hr', 'consulting', 'fortune 500']
    },
    {
      id: 'team-nishant',
      title: 'Nishant Seth',
      type: 'team',
      path: '/team',
      description: 'Practice Head- Data, AI/ML and Analytics - Data Analytics consultant with 25+ years in AI/ML, data lakes, and analytics across industries.',
      keywords: ['nishant seth', 'data', 'ai', 'ml', 'analytics', 'machine learning', 'data lakes', 'consultant']
    },
    {
      id: 'team-vinesh',
      title: 'Vinesh Singh',
      type: 'team',
      path: '/team',
      description: 'Head - L&D - Over 35 years in Finance, Accounting, strategic planning, Budgeting, Joint ventures and Vendor management',
      keywords: ['vinesh singh', 'learning', 'development', 'finance', 'accounting', 'strategic planning', 'budgeting']
    },
    {
      id: 'team-saikrishna',
      title: 'Sai Krishna',
      type: 'team',
      path: '/team',
      description: 'Head - Product & Innovation - Over 13+ years of experience managing research and development in IoT',
      keywords: ['sai krishna', 'product', 'innovation', 'research', 'development', 'iot']
    }
  ],
 
  // Client testimonials
  testimonials: [
    {
      id: 'testimonial-rajiv',
      title: 'Rajiv Narayana - Ansrsource',
      type: 'testimonial',
      path: '/testimonials',
      description: 'Makonis consistently stands out because they genuinely listen. They understand your business\'s current state and challenges, then expertly guide you to talent that fits both your capability needs and your organizational culture.',
      keywords: ['rajiv narayana', 'ansrsource', 'testimonial', 'talent', 'organizational culture', 'business challenges']
    },
    {
      id: 'testimonial-ashish',
      title: 'Ashish Joshi - Redwood Software',
      type: 'testimonial',
      path: '/testimonials',
      description: 'We highly recommend Makonis to any employer looking for hiring top talent. They are professional, genuine and highly invested in finding you the best talent.',
      keywords: ['ashish joshi', 'redwood software', 'testimonial', 'hiring', 'top talent', 'professional']
    },
    {
      id: 'testimonial-sudhakar',
      title: 'Sudhakar Krishnamachari - LoanIQ',
      type: 'testimonial',
      path: '/testimonials',
      description: 'Makonis quickly supplied quality contract-to-hire engineers for testing and programming, even boosting their training investment.',
      keywords: ['sudhakar krishnamachari', 'loaniq', 'testimonial', 'contract-to-hire', 'engineers', 'testing', 'programming']
    },
    {
      id: 'testimonial-priya',
      title: 'Priya Sharma - FinPoint',
      type: 'testimonial',
      path: '/testimonials',
      description: 'Exceptional service! Their speed, accuracy, and client support have consistently exceeded our expectations.',
      keywords: ['priya sharma', 'finpoint', 'testimonial', 'exceptional service', 'speed', 'accuracy', 'client support']
    }
  ],
 
  // Detailed testing services
  testingServices: [
    {
      id: 'testing-functional',
      title: 'Functional Testing',
      type: 'testing-service',
      path: '/testing',
      description: 'Our Functional Testing Services ensure the independent verification and validation of applications for software vendors and enterprises.',
      keywords: ['functional testing', 'manual testing', 'regression testing', 'integration testing', 'user acceptance testing', 'exploratory testing', 'compatibility testing']
    },
    {
      id: 'testing-automation',
      title: 'Test Automation',
      type: 'testing-service',
      path: '/testing',
      description: 'Our test automation Center of Excellence leverages Selenium and other leading tools for automating web application testing.',
      keywords: ['test automation', 'selenium', 'appium', 'api automation', 'continuous integration', 'bdd', 'cucumber']
    },
    {
      id: 'testing-performance',
      title: 'Performance Testing',
      type: 'testing-service',
      path: '/testing',
      description: 'We provide comprehensive performance testing services to ensure your applications can handle expected load and perform optimally.',
      keywords: ['performance testing', 'load testing', 'stress testing', 'volume testing', 'scalability testing', 'jmeter', 'loadrunner']
    },
    {
      id: 'testing-mobile',
      title: 'Mobile Testing',
      type: 'testing-service',
      path: '/testing',
      description: 'We offer mobile testing services for both functional and non-functional testing of mobile applications.',
      keywords: ['mobile testing', 'native app testing', 'hybrid app testing', 'cross-platform testing', 'device compatibility', 'usability testing']
    }
  ],
 
  // Embedded systems services
  embeddedServices: [
    {
      id: 'embedded-telematics',
      title: 'Informatics & Telematics',
      type: 'embedded-service',
      path: '/embedded',
      description: 'Our telematics solutions enable real-time data collection, analysis, and communication between vehicles, infrastructure, and cloud platforms.',
      keywords: ['telematics', 'informatics', 'connected vehicles', 'real-time data', 'vehicle communication', 'cloud platforms']
    },
    {
      id: 'embedded-automotive',
      title: 'Automotive Software',
      type: 'embedded-service',
      path: '/embedded',
      description: 'Comprehensive automotive software solutions for modern connected vehicles.',
      keywords: ['automotive software', 'vehicle systems', 'automotive embedded', 'car software', 'vehicle connectivity']
    },
    {
      id: 'embedded-testing',
      title: 'Embedded Testing Services',
      type: 'embedded-service',
      path: '/embedded',
      description: 'Our comprehensive testing services validate functionality, performance, and reliability of embedded systems.',
      keywords: ['embedded testing', 'unit testing', 'integration testing', 'system testing', 'performance testing', 'security testing', 'compliance testing']
    }
  ],
 
  // Physical design services
  physicalDesignServices: [
    {
      id: 'pd-floorplanning',
      title: 'Floorplanning',
      type: 'physical-design-service',
      path: '/physical-design',
      description: 'Strategic planning of chip layout to optimize area, power, and performance.',
      keywords: ['floorplanning', 'chip layout', 'macro placement', 'power network design', 'io planning']
    },
    {
      id: 'pd-placement',
      title: 'Placement & Routing',
      type: 'physical-design-service',
      path: '/physical-design',
      description: 'Optimal placement and routing of components for efficient chip design.',
      keywords: ['placement', 'routing', 'chip design', 'component placement', 'signal routing']
    }
  ],
 
  // Physical verification services
  physicalVerificationServices: [
    {
      id: 'pv-drc',
      title: 'Design Rule Checking (DRC)',
      type: 'physical-verification-service',
      path: '/physical-verification',
      description: 'Verifying that the layout strictly adheres to the foundry\'s design rules to prevent manufacturing defects.',
      keywords: ['drc', 'design rule checking', 'foundry rules', 'manufacturing defects', 'layout verification']
    },
    {
      id: 'pv-lvs',
      title: 'Layout vs Schematic (LVS)',
      type: 'physical-verification-service',
      path: '/physical-verification',
      description: 'Ensuring the physical layout matches the logical schematic design.',
      keywords: ['lvs', 'layout vs schematic', 'physical layout', 'schematic verification', 'design verification']
    }
  ],
 
  // Technologies and tools
  technologies: [
    {
      id: 'tech-selenium',
      title: 'Selenium',
      type: 'technology',
      path: '/testing',
      description: 'Web automation testing framework',
      keywords: ['selenium', 'web automation', 'testing framework', 'webdriver']
    },
    {
      id: 'tech-react',
      title: 'React',
      type: 'technology',
      path: '/webdev',
      description: 'Modern JavaScript library for building user interfaces',
      keywords: ['react', 'javascript', 'frontend', 'ui library', 'component-based']
    },
    {
      id: 'tech-nodejs',
      title: 'Node.js',
      type: 'technology',
      path: '/webdev',
      description: 'JavaScript runtime for server-side development',
      keywords: ['nodejs', 'javascript', 'backend', 'server-side', 'runtime']
    },
    {
      id: 'tech-python',
      title: 'Python',
      type: 'technology',
      path: '/analytics',
      description: 'Programming language for AI, ML, and data analytics',
      keywords: ['python', 'programming', 'ai', 'machine learning', 'data analytics']
    },
    {
      id: 'tech-tensorflow',
      title: 'TensorFlow',
      type: 'technology',
      path: '/analytics',
      description: 'Machine learning framework for AI applications',
      keywords: ['tensorflow', 'machine learning', 'ai', 'deep learning', 'neural networks']
    }
  ]
};

// Popular search terms for quick access
export const popularSearches = [
  'AI',
  'Web Development',
  'Testing',
  'IoT',
  'Staff Augmentation',
  'Semiconductors',
  'Team',
  'Contact'
];